<template>
  <div style="position: relative" class="video-player-wrapper">
    <div class="video-container" v-show="sourceMaterialType !== 2">
      <!-- 加载中覆盖层 -->
      <div v-show="isLoading" class="loading-overlay">
        <div class="loading-spinner">
          <svg viewBox="0 0 50 50" class="spinner">
            <circle
              cx="25"
              cy="25"
              r="20"
              fill="none"
              stroke="currentColor"
              stroke-width="4"
              stroke-linecap="round"
              stroke-dasharray="31.416"
              stroke-dashoffset="31.416"
              class="spinner-path"
            />
          </svg>
        </div>
        <div class="loading-text">缓存中...</div>
      </div>
      
      <video
        ref="videoElement"
        class="video-js vjs-default-skin"
        controls
        preload="auto"
        :poster="mediaInfo.thumbnail || ''"
        data-setup='{"html5": {"preloadTextTracks": false}}'
        playsinline
      >
        <p class="vjs-no-js">
          要查看此视频，请启用JavaScript并考虑升级到
          <a href="https://videojs.com/html5-video-support/" target="_blank">
            支持HTML5视频的Web浏览器
          </a>
        </p>
      </video>
    </div>
    
    <!-- 收藏按钮 -->
    <div class="collect" @click="handleCollect" v-if="showActions && sourceMaterialType !== 2">
      {{ isCollected ? '取消收藏' : '收藏' }}
    </div>
    
    <!-- 图片轮播（图文模式） -->
    <div class="info" v-if="sourceMaterialType === 2">
      <div class="carousel-container">
        <div class="carousel-arrow carousel-arrow--left" @click="changCarousel(0)" v-if="currentImageIndex > 0">
          <Icon name="arrow-left" :size="18" />
        </div>
        <div class="image-display">
          <el-image 
            ref="previewImageRef"
            :src="currentImage" 
            :alt="mediaInfo.title"
            class="carousel-image"
            fit="contain"
            :draggable="false"
            :preview-src-list="[]"
            :initial-index="currentImageIndex"
            :append-to-body="true"
            :preview-teleported="true"
            :z-index="9999"
            @click="handleImagePreview"
            @load="onImageLoad"
            @error="onImageError"
          />
        </div>
        <div class="carousel-arrow carousel-arrow--right" @click="changCarousel(1)" v-if="currentImageIndex < (mediaInfo.articleImage?.length || 1) - 1">
          <Icon name="arrow-right" :size="18" />
        </div>
      </div>
    </div>
    
    <!-- 独立的图片查看器组件 -->
    <el-image-viewer
      v-if="showViewer"
      :url-list="mediaInfo.articleImage || []"
      :initial-index="currentImageIndex"
      :z-index="9999"
      :teleported="true"
      @close="showViewer = false"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue'
import videojs from 'video.js'
import 'video.js/dist/video-js.css'
import Icon from './Icon.vue'
import { ElImageViewer } from 'element-plus'

// Props
const props = defineProps({
  videoUrl: {
    type: String,
    required: true
  },
  mediaInfo: {
    type: Object,
    default: () => ({})
  },
  showActions: {
    type: Boolean,
    default: true
  },
  collected: {
    type: Boolean,
    default: false
  },
  sourceMaterialType: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits(['collect', 'ready', 'play', 'pause', 'ended', 'changeArticleIndex'])

// 响应式数据
const videoElement = ref(null)
const player = ref(null)
const isCollected = ref(props.collected)
const timer = ref(null)
const currentImageIndex = ref(0)
const isLoading = ref(false)

// 计算属性
const currentImage = computed(() => {
  if (props.mediaInfo.articleImage && props.mediaInfo.articleImage.length > 0) {
    return props.mediaInfo.articleImage[currentImageIndex.value] || props.mediaInfo.articleImage[0]
  }
  return `https://picsum.photos/572/402?random=default`
})

// 播放器配置
const playerOptions = {
  inactivityTimeout: 0,
  controls: true,
  preload: "metadata",
  fluid: false,
  responsive: true,
  bigPlayButton: true,
  volume: 0.5,
  poster: props.mediaInfo.thumbnail || '',
  html5: {
    vhs: {
      overrideNative: true
    },
    nativeAudioTracks: false,
    nativeVideoTracks: false,
    preloadTextTracks: false
  },
  controlBar: {
    children: [
      { name: "playToggle" },
      { name: "volumePanel" },
      { name: "currentTimeDisplay" },
      { name: "timeDivider" },
      { name: "durationDisplay" },
      { name: "progressControl" },
      { name: "fullscreenToggle" }
    ]
  }
}

// 方法
const initPlayer = () => {
  if (!videoElement.value || !props.videoUrl) return
  
  try {
    const options = {
      ...playerOptions,
      poster: props.mediaInfo.thumbnail || '',
      autoplay: false,
      preload: 'metadata'
    };
    
    player.value = videojs(videoElement.value, options, () => {
      // 设置音量并确保控制条同步
      player.value.volume(0.5)
      player.value.autoplay(false)
      player.value.addClass('vjs-has-poster')
      player.value.removeClass('vjs-has-started')

      // 延迟确保音量控制条正确显示
      setTimeout(() => {
        player.value.volume(0.5)
        // 触发音量变化事件以更新控制条
        player.value.trigger('volumechange')
        // 强制更新音量控制条显示
        updateVolumeBar(0.5)
      }, 100)

      emit('ready', player.value)
    })
    
    setVideoSource(props.videoUrl)
    
    player.value.on('loadstart', () => {
      isLoading.value = true
      if (props.mediaInfo.thumbnail) {
        player.value.poster(props.mediaInfo.thumbnail)
        player.value.addClass('vjs-has-poster')
        player.value.removeClass('vjs-has-started')
      }
    })
    
    player.value.on('play', () => {
      player.value.addClass('vjs-has-started')
      player.value.removeClass('vjs-waiting vjs-seeking vjs-loading vjs-stalled')
      emit('play')
    })
    
    player.value.on('waiting', () => {
      isLoading.value = true
    })
    
    const hideLoading = () => {
      isLoading.value = false
    }
    
    player.value.on('canplay', hideLoading)
    player.value.on('canplaythrough', hideLoading)
    player.value.on('seeked', hideLoading)
    player.value.on('loadeddata', hideLoading)
    
    player.value.on('loadedmetadata', () => {
      // 设置音量并确保控制条同步
      player.value.volume(0.5)
      isLoading.value = false

      player.value.addClass('vjs-has-started')
      player.value.addClass('vjs-paused')
      player.value.removeClass('vjs-playing')

      // 延迟确保音量控制条正确显示
      setTimeout(() => {
        player.value.volume(0.5)
        // 触发音量变化事件以更新控制条
        player.value.trigger('volumechange')
        // 强制更新音量控制条显示
        updateVolumeBar(0.5)
      }, 100)
    })
    
    player.value.on('stalled', () => {
      isLoading.value = true
    })
    
    player.value.on('abort', () => {
      isLoading.value = false
    })
    
    player.value.on('error', () => {
      isLoading.value = false
      player.value.removeClass('vjs-waiting')
    })
    
    player.value.on('pause', () => {
      emit('pause')
    })
    
    player.value.on('ended', () => {
      // 视频结束时，确保显示播放按钮
      setTimeout(() => {
        player.value.removeClass('vjs-ended')
        player.value.addClass('vjs-paused')
        // 重要：保证播放按钮正确显示，强制添加必要的类
        player.value.removeClass('vjs-playing')
        player.value.removeClass('vjs-has-started')
        player.value.addClass('vjs-has-poster')
        
        // 恢复封面显示
        if (props.mediaInfo.thumbnail) {
          player.value.poster(props.mediaInfo.thumbnail)
        }
        
        // 强制显示大播放按钮
        const bigPlayButton = player.value.getChild('bigPlayButton').el()
        if (bigPlayButton) {
          bigPlayButton.style.display = 'block'
          bigPlayButton.style.opacity = '1'
        }
      }, 100)
      emit('ended')
    })
    
    // 监听音量变化事件，确保控制条同步
    player.value.on('volumechange', () => {
      const volume = player.value.volume()
      updateVolumeBar(volume)
    })

    nextTick(() => {
      setupVolumeDisplay()
    })
    
  } catch (error) {
    console.error('视频播放器初始化失败:', error)
  }
}

const setVideoSource = (url) => {
  if (!player.value || !url) return
  
  try {
    isLoading.value = true
    
    let sources = [];
    
    if (url.includes('.webm')) {
      sources.push({
        type: "video/webm",
        src: url
      });
    } else if (url.includes('.mov')) {
      sources.push({
        type: "video/quicktime",
        src: url
      });
    } else if (url.includes('.avi')) {
      sources.push({
        type: "video/x-msvideo",
        src: url
      });
    } else {
      sources.push({
        type: "video/mp4",
        src: url
      });
    }
    
    player.value.src(sources);
    player.value.load();
    
    const onLoadComplete = () => {
      isLoading.value = false;
      player.value.off('loadedmetadata', onLoadComplete);
    };
    player.value.one('loadedmetadata', onLoadComplete);
    
    setTimeout(() => {
      if (isLoading.value) {
        isLoading.value = false;
      }
    }, 3000);
    
  } catch (error) {
    console.error('设置视频源失败:', error);
    isLoading.value = false;
  }
}

// 更新音量控制条显示
const updateVolumeBar = (volume) => {
  try {
    if (!player.value) return

    const volumeLevel = player.value.el().querySelector('.vjs-volume-level')
    const volumeHandle = player.value.el().querySelector('.vjs-volume-handle')

    if (volumeLevel) {
      volumeLevel.style.width = `${volume * 100}%`
    }
    if (volumeHandle) {
      volumeHandle.style.left = `${volume * 100}%`
    }

    // 更新 aria 属性
    const volumeBar = player.value.el().querySelector('.vjs-volume-bar')
    if (volumeBar) {
      volumeBar.setAttribute('aria-valuenow', Math.round(volume * 100))
      volumeBar.setAttribute('aria-valuetext', `${Math.round(volume * 100)}%`)
    }
  } catch (error) {
    console.error('更新音量控制条失败:', error)
  }
}

const setupVolumeDisplay = () => {
  try {
    // 等待播放器完全初始化
    setTimeout(() => {
      if (!player.value) return

      // 确保音量控制条正确显示当前音量
      const currentVolume = player.value.volume()
      updateVolumeBar(currentVolume)

      const volumeBar = document.getElementsByClassName("vjs-volume-bar")[0]
      if (volumeBar) {
        let volumeDisplay = document.getElementById("volumeNum")
        if (!volumeDisplay) {
          volumeDisplay = document.createElement("span")
          volumeDisplay.id = "volumeNum"
          volumeBar.appendChild(volumeDisplay)
        }

        timer.value = setInterval(() => {
          if (volumeBar.ariaValueText) {
            volumeDisplay.innerHTML = volumeBar.ariaValueText
          }
        }, 1000)
      }
    }, 200)
  } catch (error) {
    console.error('设置音量显示失败:', error)
  }
}

const destroyPlayer = () => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
  
  if (player.value) {
    try {
      player.value.dispose()
      player.value = null
    } catch (error) {
      console.error('销毁视频播放器失败:', error)
    }
  }
}

const handleCollect = () => {
  isCollected.value = !isCollected.value
  emit('collect', isCollected.value)
}

const changCarousel = (direction) => {
  if (!props.mediaInfo.articleImage || props.mediaInfo.articleImage.length === 0) return
  
  if (direction === 0) {
    if (currentImageIndex.value > 0) {
      currentImageIndex.value--
      emit('changeArticleIndex', currentImageIndex.value)
    }
  } else {
    if (currentImageIndex.value < props.mediaInfo.articleImage.length - 1) {
      currentImageIndex.value++
      emit('changeArticleIndex', currentImageIndex.value)
    }
  }
}

// 处理图片预览
const previewImageRef = ref(null)
const showViewer = ref(false)

const handleImagePreview = () => {
  // 直接显示独立的图片查看器组件
  showViewer.value = true
}

const onImageLoad = () => {}

const onImageError = () => {
  console.error('图片加载失败')
}

const keyup = (e) => {
  if (e.code === "Space" && player.value) {
    nextTick(() => {
      try {
        if (player.value.paused()) {
          player.value.play()
        } else {
          player.value.pause()
        }
      } catch (error) {
        console.error('播放控制失败:', error)
      }
    })
  }
}

watch(() => props.mediaInfo, (newValue) => {
  if (player.value && newValue && newValue.thumbnail) {
    player.value.poster(newValue.thumbnail);
  }
}, { deep: true });

watch(() => props.videoUrl, (newUrl, oldUrl) => {
  if (newUrl && newUrl !== oldUrl) {
    if (player.value) {
      setVideoSource(newUrl)
    } else {
      nextTick(() => {
        initPlayer()
      })
    }
  }
})

watch(() => props.sourceMaterialType, (newType) => {
  if (newType === 2) {
    currentImageIndex.value = 0
  } else {
    if (props.videoUrl && player.value) {
      setVideoSource(props.videoUrl)
    } else if (props.videoUrl) {
      setTimeout(initPlayer, 100)
    }
  }
})

watch(() => props.collected, (newVal) => {
  isCollected.value = newVal
})

onMounted(() => {
  document.addEventListener("keyup", keyup);
  
  if (props.videoUrl) {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = props.videoUrl;
    link.as = 'video';
    document.head.appendChild(link);
  }
  
  if (props.videoUrl && props.sourceMaterialType !== 2) {
    isLoading.value = true;
    setTimeout(() => {
      initPlayer();
    }, 100);
  }
})

onUnmounted(() => {
  document.removeEventListener("keyup", keyup)
  destroyPlayer()
})

defineExpose({
  play: () => player.value?.play(),
  pause: () => player.value?.pause(),
  getCurrentTime: () => player.value?.currentTime(),
  setCurrentTime: (time) => player.value?.currentTime(time),
  getDuration: () => player.value?.duration(),
  getVolume: () => player.value?.volume(),
  setVolume: (volume) => player.value?.volume(volume),
  toggleFullscreen: () => player.value?.requestFullscreen()
})
</script>

<style>
</style>

<style lang="scss" scoped>
.video-player-wrapper {
  width: 572px;
  height: 402px;
  position: relative;
  overflow: hidden;
  background: #000;
  margin: 0 auto;
  transform: translateZ(0);
  will-change: transform;
  
  :deep(.vjs-big-play-button) {
    opacity: 1; /* Changed from 0 to 1 to make the button visible by default */
  }
}

.video-container {
  width: 100%;
  height: 100%;
  position: relative;
  transform: translateZ(0);
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(100% - 40px);
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2;
  pointer-events: none;
}

.loading-text {
  color: white;
  font-size: 12px;
  font-weight: 400;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  letter-spacing: 0.5px;
  margin-top: 8px;
  background: rgba(0, 0, 0, 0.5);
  padding: 4px 8px;
  border-radius: 12px; /* 恢复加载文本的圆角 */
  position: relative;
  top: 20px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  opacity: 0.8;
  position: relative;
  top: 20px;
}

.spinner {
  width: 100%;
  height: 100%;
  color: var(--el-color-primary, #409EFF);
  animation: spin 2s linear infinite;
  will-change: transform;
}

.spinner-path {
  animation: spinner-dash 1.5s ease-in-out infinite;
  will-change: stroke-dasharray, stroke-dashoffset;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes spinner-dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

.info {
  width: 100%;
  height: 402px;
  background-color: var(--card-hover-bg);
  border-radius: 16px;
  box-sizing: border-box;
  font-size: 16px;
  color: var(--app-text-color);
  position: relative;
  overflow: hidden;
  
  .carousel-container {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .image-display {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
      .carousel-image {
    max-width: 100%;
    max-height: 100%;
    border-radius: 16px;
    cursor: pointer;
  }
  
  .image-display :deep(.el-image) {
    width: 100%;
    height: 100%;
    border-radius: 16px;
  }
  
  .image-display :deep(.el-image__inner) {
    object-fit: contain;
    border-radius: 16px;
    cursor: pointer;
  }
  
  .carousel-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 70px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    color: white;
    transition: background-color 0.3s ease;
    
    &:hover {
      background: rgba(0, 0, 0, 0.7);
    }
    
    &--left {
      left: 20px;
    }
    
    &--right {
      right: 20px;
    }
  }
}

.collect {
  position: absolute;
  width: 52px;
  height: 20px;
  line-height: 20px;
  right: 16px;
  top: 10px;
  font-size: 10px;
  font-weight: 700;
  text-align: center;
  border-radius: 3px;
  background-color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  z-index: 999;

  &:hover {
    background-color: #ececec;
  }
}
</style>

<style>
/* Video.js 样式覆盖 */
.video-player-wrapper .video-js {
  width: 100% !important;
  height: 100% !important;
  overflow: hidden;
  background-color: #000;
}

/* 全屏时移除所有圆角和容器限制 */
.video-js.vjs-fullscreen {
  border-radius: 0 !important;
}

.video-js.vjs-fullscreen .video-player-wrapper {
  border-radius: 0 !important;
}

/* 全屏时控制栏变大 */
.video-js.vjs-fullscreen .vjs-control-bar {
  height: 60px !important;
  font-size: 18px !important;
}

/* 全屏时控制按钮变大 */
.video-js.vjs-fullscreen .vjs-control,
.video-js.vjs-fullscreen .vjs-volume-panel.vjs-volume-panel-vertical {
  width: 4em !important;
}

.video-js.vjs-fullscreen .vjs-play-control,
.video-js.vjs-fullscreen .vjs-volume-panel,
.video-js.vjs-fullscreen .vjs-fullscreen-control {
  font-size: 18px !important;
}

.video-js.vjs-fullscreen .vjs-current-time,
.video-js.vjs-fullscreen .vjs-time-divider,
.video-js.vjs-fullscreen .vjs-duration {
  font-size: 18px !important;
}

/* 全屏时进度条变大 */
.video-js.vjs-fullscreen .vjs-progress-holder {
  height: 0.5em !important;
}

/* 全屏时按钮图标变大 */
.video-js.vjs-fullscreen .vjs-icon-placeholder:before {
  font-size: 1.8em !important;
}

.video-player-wrapper .vjs-picture-in-picture-control {
  display: none !important;
}

.video-player-wrapper .vjs-remaining-time {
  display: none !important;
}

.video-player-wrapper #volumeNum {
  position: absolute;
  display: none;
}

.video-player-wrapper .vjs-volume-level {
  background: var(--el-color-primary, #409EFF);
  color: #fff;
}

.video-player-wrapper .vjs-volume-vertical {
  background: #554b4d;
  border-radius: 8px;
}

.video-player-wrapper .vjs-control-bar {
  display: flex;
  visibility: visible;
  opacity: 0;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 0 0 16px 16px;
  z-index: 10;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.video-player-wrapper:hover .vjs-control-bar {
  opacity: 1;
  pointer-events: auto;
}

/* 视频暂停时也显示控制栏 */
.video-player-wrapper .video-js.vjs-paused .vjs-control-bar {
  opacity: 1;
  pointer-events: auto;
}

/* 视频结束时也显示控制栏 */
.video-player-wrapper .video-js.vjs-ended .vjs-control-bar {
  opacity: 1;
  pointer-events: auto;
}

.video-player-wrapper .vjs-control,
.video-player-wrapper .vjs-volume-panel.vjs-volume-panel-vertical {
  width: 3em;
}

.video-player-wrapper .vjs-play-control,
.video-player-wrapper .vjs-volume-panel,
.video-player-wrapper .vjs-fullscreen-control {
  font-size: 14px;
  color: #000;
}

.video-player-wrapper .vjs-current-time,
.video-player-wrapper .vjs-time-divider,
.video-player-wrapper .vjs-duration {
  display: inline-block;
  font-size: 14px;
  color: #fff;
}

.video-player-wrapper .vjs-mute-control {
  padding-left: 1em;
  padding-right: 1em;
}

.video-player-wrapper .vjs-time-divider {
  padding: 0;
  text-align: center;
}

.video-player-wrapper .vjs-slider {
  background-color: #fff;
  border-radius: 4px;
}

.video-player-wrapper .vjs-play-progress {
  border-radius: 3px;
  background-color: var(--el-color-primary, #409EFF);
}

.video-player-wrapper .vjs-big-play-button {
  height: 100% !important;
  width: 100% !important;
  top: 0 !important;
  left: 0 !important;
  border: none !important;
  background-color: transparent !important; /* 改回透明背景 */
  text-align: center !important;
  position: absolute !important;
  z-index: 3 !important;
  cursor: pointer;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0 !important;
}

.video-player-wrapper .vjs-big-play-button .vjs-icon-placeholder {
  display: inline-block !important;
  height: 60px !important;
  width: 60px !important;
  background-color: transparent !important;
  border: none !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
  padding: 0 !important;
}

.video-player-wrapper .vjs-big-play-button:hover {
  background-color: rgba(255, 255, 255, 0.2) !important; /* 恢复原来的悬停效果 */
}

.video-player-wrapper .vjs-big-play-button .vjs-icon-placeholder::before {
  content: "" !important;
  display: none !important;
}

.video-player-wrapper .vjs-big-play-button .vjs-icon-placeholder::after {
  content: "" !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70px; /* Slightly larger icon */
  height: 70px; /* Slightly larger icon */
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 64 64' fill='%23ffffff'%3E%3Ccircle cx='32' cy='32' r='30' fill='rgba(0,0,0,0.5)' stroke='%23ffffff' stroke-width='2'/%3E%3Cpath d='M26 20 L26 44 L44 32 Z' fill='%23ffffff'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.video-player-wrapper .vjs-big-play-button:hover .vjs-icon-placeholder::after {
  opacity: 0.8;
}

.video-player-wrapper .video-js.vjs-playing .vjs-big-play-button {
  display: none !important;
}

.video-player-wrapper .video-js.vjs-paused .vjs-big-play-button {
  display: block !important;
}

/* 确保视频结束时显示播放按钮 */
.video-player-wrapper .video-js.vjs-ended .vjs-big-play-button {
  display: block !important;
  opacity: 1 !important;
}

.video-player-wrapper .vjs-loading-spinner {
  display: none !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  width: 60px !important;
  height: 60px !important;
  border: 5px solid rgba(255, 255, 255, 0.2) !important;
  border-top: 5px solid #fff !important;
  border-radius: 50% !important;
  animation: vjs-loading-spin 1s linear infinite !important;
  z-index: 1000 !important;
  background: rgba(0, 0, 0, 0.3) !important;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5) !important;
}

.video-player-wrapper .video-js.vjs-waiting .vjs-loading-spinner {
  display: block !important;
}

.video-player-wrapper .video-js.vjs-seeking .vjs-loading-spinner {
  display: block !important;
}

.video-player-wrapper .video-js.vjs-loading .vjs-loading-spinner {
  display: block !important;
}

.video-player-wrapper .video-js.vjs-stalled .vjs-loading-spinner {
  display: block !important;
}

@keyframes vjs-loading-spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@media (max-width: 768px) {
  .video-player-wrapper {
    width: 100%;
    height: 250px;
  }
}

@media (max-width: 600px) {
  .video-player-wrapper {
    width: 100%;
    height: 200px;
  }
}

.video-js .vjs-poster {
  background-size: contain;
  background-position: center center;
  background-repeat: no-repeat;
  z-index: 1;
}

/* 确保所有状态下都不显示封面 */
.video-js .vjs-poster {
  display: none !important;
}

.video-player-wrapper .vjs-control-bar {
  z-index: 10;
}

.video-player-wrapper .vjs-big-play-button {
  z-index: 2;
}
</style>