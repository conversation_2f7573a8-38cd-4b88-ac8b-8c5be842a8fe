<template>
  <el-dialog
    append-to-body
    width="50%"
    title="源调试用户协议"
    :visible.sync="visible"
  >
    <div class="header">
      <div class="title">全部消息</div>
      <iconpark-icon
        name="close-small"
        class="close-small"
        @click="visible = false"
      ></iconpark-icon>
    </div>
    <div class="mainBox">
      <p class="h1">第一章 概述</p>
      <p class="h2">第1条 本协议</p>
      <p class="t">
        本《源调试用户协议》（以下简称“本协议”）由您（以下简称“用户”）与源调试应用开发者（以下简称“开发者”）共同签订。在您开始使用源调试软件及相关服务前，请务必仔细阅读本协议，确保您已充分理解本协议中各条款。您一旦开始使用源调试软件及相关服务，即表示您已阅读并同意本协议的全部条款，本协议立即在您与开发者之间产生法律效力。
      </p>
      <p class="h2">第2条 范围</p>
      <p class="t">
        本协议适用于用户使用源调试软件及相关服务的一切活动。用户在使用源调试软件及相关服务时，应遵守本协议的约定。如用户违反本协议的任何条款，开发者有权采取相应措施，包括但不限于暂停或终止向用户提供服务、追究用户法律责任等。
      </p>
      <p class="h1">第二章 用户权利与义务</p>
      <p class="h2">第3条 用户权利</p>
      <p class="t">1、用户有权按照本协议的约定，使用源调试软件及相关服务。</p>
      <p class="t">
        2、用户有权随时修改、删除其在源调试软件中的个人信息、设置和内容。
      </p>
      <p class="t">
        3、用户有权在使用源调试软件及相关服务过程中，依法提出投诉、举报、建议或意见。
      </p>
      <p class="h2">第4条 用户义务</p>
      <p class="t">
        1、用户应当保证其为具有完全民事行为能力的自然人，且具备使用源调试软件及相关服务的资格。
      </p>
      <p class="t">
        2、用户应当遵守本协议及相关法律法规的规定，不得使用源调试软件及相关服务从事违法违规行为，包括但不限于侵犯他人权益、传播违法信息、侵犯他人知识产权等。
      </p>
      <p class="t">
        3、用户应当保护好自己的账号、密码等信息，不得将其提供给他人使用。如因用户自身原因导致账号、密码等信息泄露，用户应自行承担相应责任。
      </p>
      <p class="t">
        4、用户应当及时更新其在源调试软件中的个人信息，以确保信息的真实、准确、完整和及时性。
      </p>
      <p class="h1">第三章 开发者权利与义务</p>
      <p class="h2">第5条 开发者权利</p>
      <p class="t">
        1、开发者有权根据用户的需求，向用户提供源调试软件及相关服务。
      </p>
      <p class="t">2、开发者有权依法收取与源调试软件及相关服务有关的费用。</p>
      <p class="t">3、开发者有权对源调试软件进行升级、维护、优化等操作。</p>
      <p class="t">
        4、如用户违反本协议的任何条款，开发者有权采取相应措施，包括但不限于暂停或终止向用户提供服务、追究用户法律责任等。
      </p>
      <p class="h2">第6条 开发者义务</p>
      <p class="t">
        1、开发者应当按照本协议的约定，向用户提供源调试软件及相关服务，确保服务的质量和安全性。
      </p>
      <p class="t">
        2、开发者应当保护用户的个人信息和隐私，不得将其泄露给任何第三方，除非根据法律法规的规定或经用户同意。
      </p>
      <p class="t">
        3、开发者应当及时处理用户的投诉、举报、建议或意见，维护用户合法权益。
      </p>
      <p class="h1">第四章 服务内容与费用</p>
      <p class="h2">第7条 服务内容</p>
      <p class="t">
        1、源调试软件及相关服务包括但不限于：协助用户安装电脑开播软件和插件、提供软件技术支持、提供插件安装指导等。
      </p>
      <p class="t">
        2、开发者可根据实际情况调整、完善源调试软件及相关服务的内容，但应当保证服务的质量和安全性。
      </p>
      <p class="h2">第8条 服务费用</p>
      <p class="t">
        1、用户使用源调试软件及相关服务可能需要支付一定的费用，具体费用标准及支付方式以源调试软件内相关页面的说明为准。
      </p>
      <p class="t">
        2、用户应当按照源调试软件内相关页面的说明支付费用，逾期未支付的，开发者有权暂停或终止向用户提供服务。
      </p>
      <p class="h1">第五章 知识产权</p>
      <p class="h2">第9条 知识产权声明</p>
      <p class="t">
        1、源调试软件的著作权、专利权、商标权等知识产权，以及与源调试软件相关的所有信息内容（包括但不限于文字、图片、音频、视频等），均归开发者所有。
      </p>
      <p class="t">
        2、未经开发者书面许可，用户不得以任何形式擅自使用、修改、传播、出版或以其他方式侵犯源调试软件的知识产权。
      </p>
      <p class="t">
        3、如用户发现任何侵犯源调试软件知识产权的行为，应当立即向开发者报告，开发者将依法采取措施维护自己的合法权益。
      </p>
      <p class="h1">第六章 免责与赔偿</p>
      <p class="h2">第10条 免责声明</p>
      <p class="t">
        1、用户使用源调试软件及相关服务的风险由用户自行承担。开发者不对源调试软件及相关服务作任何明示或暗示的担保，包括但不限于源调试软件及相关服务的适用性、无错误或病毒、稳定性、可靠性、准确性等。
      </p>
      <p class="t">
        2、开发者不承担因不可抗力或其他非开发者原因导致的源调试软件及相关服务中断、延迟、数据丢失等问题。
      </p>
      <p class="t">
        3、开发者不承担因用户违反本协议约定或相关法律法规规定，使用源调试软件及相关服务所导致的任何损失。
      </p>
      <p class="h2">第11条 赔偿责任</p>
      <p class="t">
        1、如用户违反本协议的任何条款，导致开发者遭受损失（包括但不限于财产损失、声誉损失、第三方索赔等），用户应当承担相应的赔偿责任。
      </p>
      <p class="t">
        2、如用户使用源调试软件及相关服务侵犯他人权益，导致开发者遭受损失或承担法律责任，用户应当承担相应的赔偿责任。
      </p>
      <p class="h1">第七章 保密条款</p>
      <p class="h2">第12条 保密义务</p>
      <p class="t">
        1、本协议中涉及的用户个人信息、源调试软件的技术细节等均为保密信息。未经对方同意，任何一方不得向第三方披露保密信息。
      </p>
      <p class="t">
        2、本协议的保密义务不适用于以下情形：(1)信息在公开领域已知；(2)信息在披露前已经为非保密状态；(3)信息在披露后由第三方合法获得；(4)法律法规要求披露的信息。
      </p>
      <p class="h1">第八章 协议终止与解除</p>
      <p class="h2">第13条 协议终止</p>
      <p class="t">
        1、本协议在以下情况下自动终止：(1)用户停止使用源调试软件及相关服务；(2)开发者停止提供源调试软件及相关服务；(3)其他法律法规规定的终止情况。
      </p>
      <p class="t">
        2、本协议终止后，用户应立即停止使用源调试软件及相关服务，并承担因未按照本协议规定停止使用而导致的一切法律责任。
      </p>
      <p class="h2">第14条 协议解除</p>
      <p class="t">
        1、如用户违反本协议的任何条款，开发者有权随时解除本协议，并要求用户承担相应的法律责任。
      </p>
      <p class="t">
        2、如开发者未履行本协议约定的义务，用户有权解除本协议，并要求开发者承担相应的法律责任。
      </p>
      <p class="h1">第九章 法律适用与争议解决</p>
      <p class="h2">第15条 法律适用</p>
      <p class="t">
        本协议的订立、履行、解释和争议解决均适用中华人民共和国法律。
      </p>
      <p class="h2">第16条 争议解决</p>
      <p class="t">
        1、本协议在履行过程中，如发生任何争议，双方应首先友好协商解决。协商不成的，任何一方均有权将争议提交至被告所在地有管辖权的人民法院诉讼解决。
      </p>
      <p class="t">
        2、在争议解决过程中，双方应继续履行本协议不涉及争议的条款。
      </p>
      <p class="h1">第十章 其他条款</p>
      <p class="h2">第17条 本协议的修改与更新</p>
      <p class="t">
        开发者有权根据业务发展、法律法规变化等情况对本协议进行修改。修改后的协议自发布之日起生效。如用户不同意修改后的协议内容，应立即停止使用源调试软件及相关服务；如用户继续使用源调试软件及相关服务的，视为接受修改后的协议内容。
      </p>
      <p class="h2">第18条 通知与送达</p>
      <p class="t">
        本协议项下的通知应以电子邮件、短信、在源调试软件内公告等方式进行。开发者向用户发出的通知，以发送完成之日起生效；用户向开发者发出的通知，以接收完成之日起生效。
      </p>
      <p class="h2">第19条 部分无效</p>
      <p class="t">
        如本协议的任何条款被认定为无效、非法或不可执行，该条款应视为可分割，不影响本协议其他条款的有效性、合法性或可执行性。
      </p>
      <p class="h2">第20条 权利放弃</p>
      <p class="t">
        任何一方未行使或延迟行使本协议项下的任何权利，不应被视为对该权利的放弃，也不影响该方在将来行使该权利。
      </p>
      <p class="h2">第21条 协议传承</p>
      <p class="t">
        本协议对双方及其各自的继承人、受让人、承包商和代理商具有约束力。
      </p>
      <p class="h2">第22条 协议标题</p>
      <p class="t">
        本协议的标题仅为方便阅读而设置，不具有实质性法律意义，不能作为解释本协议内容的依据。
      </p>
      <p class="h2">第23条 附加条款</p>
      <p class="t">
        本协议未涉及的问题参照相关法律法规，当本协议与国家法律法规冲突时，以国家法律法规为准。
      </p>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
    };
  },
};
</script>

<style scoped lang="scss">
::v-deep .el-dialog__header {
  display: none;
}
::v-deep .el-dialog {
  background: rgba($color: #000000, $alpha: 0) !important;
}
::v-deep .el-dialog__body {
  height: 380px !important;
  width: 500px;
  border-radius: 9px;
  padding: 16px;
  box-sizing: border-box;
  .header {
    display: flex;
    justify-content: space-between;
    position: relative;
    .close-small {
      cursor: pointer;
      position: absolute;
      right: 0px;
    }
    .title {
      font-size: 14px;
      font-weight: 700;
    }
  }
  .mainBox {
    height: 300px;
		margin-top: 16px;
    width: 100%;
    overflow-y: scroll;
    &::-webkit-scrollbar {
      width: 10px;
      height: 10px;
    }
    &::-webkit-scrollbar-thumb {
      background: linear-gradient(to bottom right, #b2b2b2 0%, #b2b2b2 100%);
      border-radius: 8px;
    }
    &::-webkit-scrollbar-button {
      opacity: 0;
    }
  }
}
p {
  user-select: none;
}

.h1 {
  margin: 8px 0;
  font-weight: 700;
}

.h2 {
  margin: 4px 0;
  font-size: 12px;
}

.t {
  margin: 0;
  font-size: 12px;
  text-indent: 1em;
}
</style>