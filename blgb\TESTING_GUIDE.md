# 视频控制栏自动隐藏功能测试指南

## 快速测试步骤

### 1. 基本功能测试
```
1. 启动应用
2. 播放任意视频
3. 进入全屏模式
4. 停止移动鼠标
5. 等待3秒 → 控制栏应该淡出隐藏
6. 移动鼠标 → 控制栏应该立即显示
```

### 2. 暂停状态测试
```
1. 播放视频并进入全屏
2. 点击暂停按钮
3. 停止移动鼠标
4. 等待超过3秒 → 控制栏应该保持显示（不隐藏）
```

### 3. 控制栏交互测试
```
1. 播放视频并进入全屏
2. 将鼠标移动到控制栏上
3. 停止移动鼠标
4. 等待超过3秒 → 控制栏应该保持显示（不隐藏）
5. 移开鼠标
6. 等待3秒 → 控制栏应该隐藏
```

### 4. 缓冲状态测试
```
1. 播放网络视频（可能会缓冲）
2. 进入全屏模式
3. 在视频缓冲时停止移动鼠标
4. 控制栏应该保持显示（方便用户了解缓冲状态）
```

## 预期行为

### ✅ 正常情况
- 播放状态 + 鼠标不动3秒 = 控制栏隐藏
- 播放状态 + 鼠标移动 = 控制栏显示
- 暂停状态 = 控制栏始终显示
- 鼠标悬停在控制栏上 = 控制栏保持显示
- 缓冲/加载状态 = 控制栏保持显示

### ❌ 异常情况需要检查
- 控制栏不隐藏（可能是inactivityTimeout设置问题）
- 控制栏不显示（可能是CSS冲突）
- 动画不流畅（可能是transition设置问题）
- 全屏时鼠标指针不隐藏（可能是cursor样式问题）

## 故障排除

### 如果控制栏不自动隐藏：
1. 检查 `inactivityTimeout` 是否设置为 3000
2. 检查是否有CSS规则覆盖了opacity设置
3. 确认视频处于播放状态（不是暂停）

### 如果控制栏不显示：
1. 检查CSS中是否有 `display: none` 规则
2. 确认鼠标移动事件是否被正确监听
3. 检查是否有JavaScript错误

### 如果动画不流畅：
1. 检查transition设置是否正确
2. 确认浏览器支持CSS transitions
3. 检查是否有性能问题影响动画

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## 移动设备测试

在移动设备上测试时，注意：
- 触摸交互应该能显示控制栏
- 触摸控制栏应该保持显示
- 停止触摸后应该正常隐藏
